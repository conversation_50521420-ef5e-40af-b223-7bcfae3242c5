<?php
/**
 * Debug Course Info Integration
 *
 * This file helps debug the course info integration issues
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add debug info to product pages
add_action('woocommerce_single_product_summary', function() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    global $product;
    
    echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; font-size: 12px;">';
    echo '<strong>DEBUG - Course Info Integration</strong><br>';
    
    // Check product ID
    echo 'Product ID: ' . $product->get_id() . '<br>';
    
    // Check if it's a Tutor product
    $is_tutor_product = get_post_meta($product->get_id(), '_tutor_product', true);
    echo 'Is Tutor Product: ' . ($is_tutor_product === 'yes' ? 'YES' : 'NO') . ' (Value: ' . $is_tutor_product . ')<br>';
    
    // Check for course connection
    global $wpdb;
    $course_id = $wpdb->get_var($wpdb->prepare(
        "SELECT post_id FROM {$wpdb->postmeta} 
         WHERE meta_key = '_tutor_course_product_id' 
         AND meta_value = %d",
        $product->get_id()
    ));
    
    echo 'Connected Course ID: ' . ($course_id ? $course_id : 'NONE') . '<br>';
    
    if ($course_id) {
        $course = get_post($course_id);
        echo 'Course Title: ' . ($course ? $course->post_title : 'NOT FOUND') . '<br>';
        echo 'Course Status: ' . ($course ? $course->post_status : 'N/A') . '<br>';
    }
    
    // Check if class exists
    echo 'WooCustom_Course_Info Class: ' . (class_exists('WooCustom_Course_Info') ? 'EXISTS' : 'NOT EXISTS') . '<br>';
    
    // Check if Tutor LMS is active
    echo 'Tutor LMS Active: ' . (class_exists('TUTOR') ? 'YES' : 'NO') . '<br>';
    
    // Check hooks
    global $wp_filter;
    $hook_found = false;
    if (isset($wp_filter['woocommerce_product_tabs'])) {
        foreach ($wp_filter['woocommerce_product_tabs']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'WooCustom_Course_Info') {
                    $hook_found = true;
                    echo 'Hook Registered: YES (Priority: ' . $priority . ')<br>';
                    break 2;
                }
            }
        }
    }
    
    if (!$hook_found) {
        echo 'Hook Registered: NO<br>';
    }
    
    echo '</div>';
}, 25);

// Add debug to product tabs
add_filter('woocommerce_product_tabs', function($tabs) {
    if (!current_user_can('manage_options')) {
        return $tabs;
    }
    
    global $product;
    
    // Add debug tab
    $tabs['debug_info'] = array(
        'title'    => 'DEBUG',
        'priority' => 99,
        'callback' => function() use ($product) {
            echo '<div style="padding: 20px;">';
            echo '<h3>Debug Information</h3>';
            
            echo '<h4>Product Information:</h4>';
            echo '<ul>';
            echo '<li>Product ID: ' . $product->get_id() . '</li>';
            echo '<li>Product Type: ' . $product->get_type() . '</li>';
            echo '<li>Product Status: ' . $product->get_status() . '</li>';
            echo '</ul>';
            
            echo '<h4>Tutor LMS Meta:</h4>';
            $tutor_meta = get_post_meta($product->get_id(), '_tutor_product', true);
            echo '<ul>';
            echo '<li>_tutor_product: ' . ($tutor_meta ? $tutor_meta : 'NOT SET') . '</li>';
            echo '</ul>';
            
            echo '<h4>Course Connection:</h4>';
            global $wpdb;
            $course_id = $wpdb->get_var($wpdb->prepare(
                "SELECT post_id FROM {$wpdb->postmeta} 
                 WHERE meta_key = '_tutor_course_product_id' 
                 AND meta_value = %d",
                $product->get_id()
            ));
            
            if ($course_id) {
                $course = get_post($course_id);
                echo '<ul>';
                echo '<li>Course ID: ' . $course_id . '</li>';
                echo '<li>Course Title: ' . ($course ? $course->post_title : 'NOT FOUND') . '</li>';
                echo '<li>Course Status: ' . ($course ? $course->post_status : 'N/A') . '</li>';
                echo '</ul>';
                
                // Test course info retrieval
                if (class_exists('WooCustom_Course_Info')) {
                    echo '<h4>Course Info Test:</h4>';
                    $reflection = new ReflectionClass('WooCustom_Course_Info');
                    $method = $reflection->getMethod('get_course_information');
                    $method->setAccessible(true);
                    $instance = WooCustom_Course_Info::instance();
                    
                    try {
                        $course_info = $method->invoke($instance, $course_id);
                        echo '<pre>' . print_r($course_info, true) . '</pre>';
                    } catch (Exception $e) {
                        echo '<p style="color: red;">Error: ' . $e->getMessage() . '</p>';
                    }
                }
            } else {
                echo '<p>No course connected to this product.</p>';
            }
            
            echo '<h4>All Product Meta:</h4>';
            $all_meta = get_post_meta($product->get_id());
            echo '<pre style="max-height: 300px; overflow-y: auto;">' . print_r($all_meta, true) . '</pre>';
            
            echo '</div>';
        }
    );
    
    return $tabs;
}, 99);

// Add admin notice for debug mode
add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-warning">';
        echo '<p><strong>Woo Custom Debug Mode Active:</strong> ';
        echo 'Course info debug information is being displayed on product pages. ';
        echo 'Remove debug-course-info.php to disable.';
        echo '</p>';
        echo '</div>';
    }
});
?>
