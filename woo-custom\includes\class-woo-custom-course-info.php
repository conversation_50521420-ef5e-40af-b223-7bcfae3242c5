<?php
/**
 * WooCustom Course Info Class
 *
 * Handles Tutor LMS course information integration with WooCommerce product pages
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WooCustom Course Info Class
 */
class WooCustom_Course_Info {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add course info tab to WooCommerce product tabs
        add_filter('woocommerce_product_tabs', array($this, 'add_course_info_tab'), 25);
    }
    
    /**
     * Add course info tab to product tabs
     *
     * @param array $tabs Existing tabs
     * @return array Modified tabs
     */
    public function add_course_info_tab($tabs) {
        global $product;

        // Check if this is a Tutor LMS product
        if (!$this->is_tutor_product($product)) {
            return $tabs;
        }

        // Add course info tab
        $tabs['course_info'] = array(
            'title'    => __('Kurs Bilgileri', 'woo-custom'),
            'priority' => 25,
            'callback' => array($this, 'course_info_tab_content'),
        );

        return $tabs;
    }
    
    /**
     * Course info tab content
     *
     * @param string $key Tab key
     * @param array $tab Tab data
     */
    public function course_info_tab_content($key, $tab) {
        global $product;

        $course_id = $this->get_course_id_by_product($product->get_id());

        if (!$course_id) {
            echo '<div class="woo-custom-course-info">';
            echo '<p>' . __('Kurs bilgileri bulunamadı.', 'woo-custom') . '</p>';
            echo '<p>' . __('Bu ürün için henüz bir kurs tanımlanmamış.', 'woo-custom') . '</p>';
            echo '</div>';
            return;
        }

        // Get course information
        $course_info = $this->get_course_information($course_id);

        // Load template
        $this->load_course_info_template($course_info);
    }
    
    /**
     * Check if product is a Tutor LMS product
     *
     * @param WC_Product $product Product object
     * @return bool
     */
    private function is_tutor_product($product) {
        if (!$product) {
            return false;
        }

        // Check if Tutor LMS is active
        if (!class_exists('TUTOR')) {
            return false;
        }

        $product_id = $product->get_id();

        // Primary method: Check if "Tutor için" checkbox is checked
        // This corresponds to the checkbox shown in the admin interface
        $is_tutor_enabled = get_post_meta($product_id, '_tutor_product', true);

        return $is_tutor_enabled === 'yes';
    }
    
    /**
     * Get course ID by product ID
     *
     * @param int $product_id Product ID
     * @return int|false Course ID or false if not found
     */
    private function get_course_id_by_product($product_id) {
        global $wpdb;

        // Method 1: Standard Tutor LMS way - find course with this product ID
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta}
             WHERE meta_key = '_tutor_course_product_id'
             AND meta_value = %d",
            $product_id
        ));

        if ($course_id) {
            return (int) $course_id;
        }

        // Method 2: Check if product itself has course ID meta
        $direct_course_id = get_post_meta($product_id, '_course_id', true);
        if ($direct_course_id) {
            return (int) $direct_course_id;
        }

        // Method 3: Check tutor_course_id meta
        $tutor_course_id = get_post_meta($product_id, 'tutor_course_id', true);
        if ($tutor_course_id) {
            return (int) $tutor_course_id;
        }

        // Method 4: Check if product and course have same name/slug
        $product = get_post($product_id);
        if ($product) {
            $course_by_name = get_page_by_title($product->post_title, OBJECT, 'courses');
            if ($course_by_name) {
                return (int) $course_by_name->ID;
            }

            // Try by slug
            $course_by_slug = get_page_by_path($product->post_name, OBJECT, 'courses');
            if ($course_by_slug) {
                return (int) $course_by_slug->ID;
            }
        }

        // Method 5: Alternative meta key patterns
        $alternative_keys = array(
            '_tutor_course_id',
            'course_id',
            '_related_course_id'
        );

        foreach ($alternative_keys as $meta_key) {
            $course_id = get_post_meta($product_id, $meta_key, true);
            if ($course_id && is_numeric($course_id)) {
                // Verify it's actually a course
                $course = get_post($course_id);
                if ($course && $course->post_type === 'courses') {
                    return (int) $course_id;
                }
            }
        }

        return false;
    }
    
    /**
     * Get course information
     *
     * @param int $course_id Course ID
     * @return array Course information
     */
    private function get_course_information($course_id) {
        $course_info = array();
        
        // Get course post
        $course = get_post($course_id);
        if (!$course) {
            return $course_info;
        }
        
        // Course level
        $course_info['level'] = $this->get_course_level($course_id);
        
        // Enrolled students count
        $course_info['enrolled_count'] = $this->get_enrolled_students_count($course_id);
        
        // Course curriculum
        $course_info['curriculum'] = $this->get_course_curriculum($course_id);
        
        // Course duration
        $course_info['duration'] = $this->get_course_duration($course_id);
        
        // Course rating
        $course_info['rating'] = $this->get_course_rating($course_id);
        
        // Course instructor
        $course_info['instructor'] = $this->get_course_instructor($course_id);
        
        // Course video
        $course_info['video'] = $this->get_course_video($course_id);
        
        return $course_info;
    }
    
    /**
     * Get course level
     *
     * @param int $course_id Course ID
     * @return string Course level
     */
    private function get_course_level($course_id) {
        if (function_exists('get_tutor_course_level')) {
            $level = get_tutor_course_level($course_id);
            return $level ? $level : __('Belirtilmemiş', 'woo-custom');
        }
        
        $level = get_post_meta($course_id, '_tutor_course_level', true);
        
        $levels = array(
            'beginner' => __('Başlangıç', 'woo-custom'),
            'intermediate' => __('Orta', 'woo-custom'),
            'advanced' => __('İleri', 'woo-custom'),
        );
        
        return isset($levels[$level]) ? $levels[$level] : __('Belirtilmemiş', 'woo-custom');
    }
    
    /**
     * Get enrolled students count
     *
     * @param int $course_id Course ID
     * @return int Students count
     */
    private function get_enrolled_students_count($course_id) {
        if (function_exists('tutor_utils')) {
            return tutor_utils()->count_enrolled_users_by_course($course_id);
        }
        
        return 0;
    }
    
    /**
     * Get course curriculum
     *
     * @param int $course_id Course ID
     * @return array Curriculum data
     */
    private function get_course_curriculum($course_id) {
        $curriculum = array();
        
        if (!function_exists('tutor_utils')) {
            return $curriculum;
        }
        
        // Get course topics
        $topics = tutor_utils()->get_topics($course_id);
        
        if ($topics->have_posts()) {
            foreach ($topics->get_posts() as $topic) {
                $topic_data = array(
                    'id' => $topic->ID,
                    'title' => $topic->post_title,
                    'contents' => array()
                );
                
                // Get topic contents
                $topic_contents = tutor_utils()->get_course_contents_by_topic($topic->ID, -1);
                
                if ($topic_contents->have_posts()) {
                    foreach ($topic_contents->get_posts() as $content) {
                        $content_type = $this->get_content_type_label($content->post_type);
                        
                        $topic_data['contents'][] = array(
                            'id' => $content->ID,
                            'title' => $content->post_title,
                            'type' => $content_type,
                            'is_preview' => get_post_meta($content->ID, '_is_preview', true) === 'yes'
                        );
                    }
                }
                
                $curriculum[] = $topic_data;
            }
        }
        
        return $curriculum;
    }
    
    /**
     * Get content type label
     *
     * @param string $post_type Post type
     * @return string Content type label
     */
    private function get_content_type_label($post_type) {
        $types = array(
            'lesson' => __('Ders', 'woo-custom'),
            'tutor_quiz' => __('Quiz', 'woo-custom'),
            'tutor_assignments' => __('Ödev', 'woo-custom'),
            'tutor_zoom_meeting' => __('Zoom Toplantısı', 'woo-custom'),
        );
        
        return isset($types[$post_type]) ? $types[$post_type] : __('İçerik', 'woo-custom');
    }
    
    /**
     * Get course duration
     *
     * @param int $course_id Course ID
     * @return string Course duration
     */
    private function get_course_duration($course_id) {
        $duration = get_post_meta($course_id, '_course_duration', true);
        
        if (!$duration) {
            return __('Belirtilmemiş', 'woo-custom');
        }
        
        return $duration;
    }
    
    /**
     * Get course rating
     *
     * @param int $course_id Course ID
     * @return array Rating data
     */
    private function get_course_rating($course_id) {
        $rating_data = array(
            'average' => 0,
            'count' => 0
        );
        
        if (function_exists('tutor_utils')) {
            $rating = tutor_utils()->get_course_rating($course_id);
            if ($rating) {
                $rating_data['average'] = $rating->rating_avg;
                $rating_data['count'] = $rating->rating_count;
            }
        }
        
        return $rating_data;
    }
    
    /**
     * Get course instructor
     *
     * @param int $course_id Course ID
     * @return array Instructor data
     */
    private function get_course_instructor($course_id) {
        $course = get_post($course_id);
        if (!$course) {
            return array();
        }
        
        $instructor = get_userdata($course->post_author);
        if (!$instructor) {
            return array();
        }
        
        return array(
            'id' => $instructor->ID,
            'name' => $instructor->display_name,
            'avatar' => get_avatar_url($instructor->ID),
            'bio' => get_user_meta($instructor->ID, 'tutor_profile_bio', true)
        );
    }
    
    /**
     * Get course video
     *
     * @param int $course_id Course ID
     * @return string Video URL or embed code
     */
    private function get_course_video($course_id) {
        $video = get_post_meta($course_id, '_video', true);
        
        if (!$video) {
            return '';
        }
        
        return $video;
    }
    
    /**
     * Load course info template
     *
     * @param array $course_info Course information
     */
    private function load_course_info_template($course_info) {
        // Check if template exists in theme
        $template_path = 'woocommerce/single-product/tabs/course-info-tab.php';
        $theme_template = locate_template($template_path);

        if ($theme_template) {
            include $theme_template;
        } else {
            // Load plugin template
            $plugin_template = WOO_CUSTOM_PLUGIN_DIR . 'templates/course-info-tab.php';
            if (file_exists($plugin_template)) {
                // Make course_info available to template
                extract(array('course_info' => $course_info));
                include $plugin_template;
            } else {
                // Fallback template
                $this->render_fallback_template($course_info);
            }
        }
    }
    
    /**
     * Render fallback template
     *
     * @param array $course_info Course information
     */
    private function render_fallback_template($course_info) {
        echo '<div class="woo-custom-course-info">';
        
        // Course level
        if (!empty($course_info['level'])) {
            echo '<div class="course-level">';
            echo '<h4>' . __('Kurs Seviyesi', 'woo-custom') . '</h4>';
            echo '<p>' . esc_html($course_info['level']) . '</p>';
            echo '</div>';
        }
        
        // Enrolled students
        if (isset($course_info['enrolled_count'])) {
            echo '<div class="enrolled-count">';
            echo '<h4>' . __('Kayıtlı Öğrenci Sayısı', 'woo-custom') . '</h4>';
            echo '<p>' . number_format($course_info['enrolled_count']) . ' ' . __('öğrenci', 'woo-custom') . '</p>';
            echo '</div>';
        }
        
        // Course curriculum
        if (!empty($course_info['curriculum'])) {
            echo '<div class="course-curriculum">';
            echo '<h4>' . __('Kurs Müfredatı', 'woo-custom') . '</h4>';
            echo '<div class="curriculum-list">';
            
            foreach ($course_info['curriculum'] as $topic) {
                echo '<div class="curriculum-topic">';
                echo '<h5>' . esc_html($topic['title']) . '</h5>';
                
                if (!empty($topic['contents'])) {
                    echo '<ul class="topic-contents">';
                    foreach ($topic['contents'] as $content) {
                        $preview_class = $content['is_preview'] ? ' preview' : '';
                        echo '<li class="content-item' . $preview_class . '">';
                        echo '<span class="content-type">' . esc_html($content['type']) . '</span>';
                        echo '<span class="content-title">' . esc_html($content['title']) . '</span>';
                        if ($content['is_preview']) {
                            echo '<span class="preview-badge">' . __('Önizleme', 'woo-custom') . '</span>';
                        }
                        echo '</li>';
                    }
                    echo '</ul>';
                }
                
                echo '</div>';
            }
            
            echo '</div>';
            echo '</div>';
        }
        
        echo '</div>';
    }
}
