<?php
/**
 * Course Info Tab Template
 *
 * This template displays Tutor LMS course information in WooCommerce product tabs
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Ensure $course_info is available
if (!isset($course_info) || empty($course_info)) {
    echo '<p>' . __('Kurs bilgileri bulunamadı.', 'woo-custom') . '</p>';
    return;
}
?>

<div class="woo-custom-course-info">
    
    <?php if (!empty($course_info['level']) || isset($course_info['enrolled_count']) || !empty($course_info['duration'])): ?>
    <div class="course-overview">
        <h3><?php _e('Kurs Genel Bilgileri', 'woo-custom'); ?></h3>
        
        <div class="course-meta-grid">
            <?php if (!empty($course_info['level'])): ?>
            <div class="course-meta-item">
                <div class="meta-icon">
                    <i class="dashicons dashicons-awards"></i>
                </div>
                <div class="meta-content">
                    <h4><?php _e('Kurs Seviyesi', 'woo-custom'); ?></h4>
                    <p><?php echo esc_html($course_info['level']); ?></p>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (isset($course_info['enrolled_count'])): ?>
            <div class="course-meta-item">
                <div class="meta-icon">
                    <i class="dashicons dashicons-groups"></i>
                </div>
                <div class="meta-content">
                    <h4><?php _e('Kayıtlı Öğrenci', 'woo-custom'); ?></h4>
                    <p><?php echo number_format($course_info['enrolled_count']); ?> <?php _e('öğrenci', 'woo-custom'); ?></p>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($course_info['duration'])): ?>
            <div class="course-meta-item">
                <div class="meta-icon">
                    <i class="dashicons dashicons-clock"></i>
                </div>
                <div class="meta-content">
                    <h4><?php _e('Kurs Süresi', 'woo-custom'); ?></h4>
                    <p><?php echo esc_html($course_info['duration']); ?></p>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($course_info['rating']) && $course_info['rating']['count'] > 0): ?>
            <div class="course-meta-item">
                <div class="meta-icon">
                    <i class="dashicons dashicons-star-filled"></i>
                </div>
                <div class="meta-content">
                    <h4><?php _e('Değerlendirme', 'woo-custom'); ?></h4>
                    <p>
                        <span class="rating-average"><?php echo number_format($course_info['rating']['average'], 1); ?></span>
                        <span class="rating-count">(<?php echo number_format($course_info['rating']['count']); ?> <?php _e('değerlendirme', 'woo-custom'); ?>)</span>
                    </p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($course_info['instructor'])): ?>
    <div class="course-instructor">
        <h3><?php _e('Eğitmen', 'woo-custom'); ?></h3>
        
        <div class="instructor-info">
            <div class="instructor-avatar">
                <img src="<?php echo esc_url($course_info['instructor']['avatar']); ?>" 
                     alt="<?php echo esc_attr($course_info['instructor']['name']); ?>" 
                     class="avatar">
            </div>
            <div class="instructor-details">
                <h4><?php echo esc_html($course_info['instructor']['name']); ?></h4>
                <?php if (!empty($course_info['instructor']['bio'])): ?>
                <p class="instructor-bio"><?php echo wp_kses_post($course_info['instructor']['bio']); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($course_info['video'])): ?>
    <div class="course-video">
        <h3><?php _e('Kurs Tanıtım Videosu', 'woo-custom'); ?></h3>
        
        <div class="video-container">
            <?php
            // Check if it's a URL or embed code
            if (filter_var($course_info['video'], FILTER_VALIDATE_URL)) {
                // It's a URL, create a simple video element or iframe
                if (strpos($course_info['video'], 'youtube.com') !== false || strpos($course_info['video'], 'youtu.be') !== false) {
                    // YouTube video
                    $video_id = '';
                    if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $course_info['video'], $matches)) {
                        $video_id = $matches[1];
                    }
                    if ($video_id) {
                        echo '<iframe width="100%" height="315" src="https://www.youtube.com/embed/' . esc_attr($video_id) . '" frameborder="0" allowfullscreen></iframe>';
                    }
                } elseif (strpos($course_info['video'], 'vimeo.com') !== false) {
                    // Vimeo video
                    $video_id = '';
                    if (preg_match('/vimeo\.com\/(\d+)/', $course_info['video'], $matches)) {
                        $video_id = $matches[1];
                    }
                    if ($video_id) {
                        echo '<iframe width="100%" height="315" src="https://player.vimeo.com/video/' . esc_attr($video_id) . '" frameborder="0" allowfullscreen></iframe>';
                    }
                } else {
                    // Other video URL
                    echo '<video width="100%" height="315" controls>';
                    echo '<source src="' . esc_url($course_info['video']) . '">';
                    echo __('Tarayıcınız video oynatmayı desteklemiyor.', 'woo-custom');
                    echo '</video>';
                }
            } else {
                // It might be embed code
                echo wp_kses_post($course_info['video']);
            }
            ?>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($course_info['curriculum'])): ?>
    <div class="course-curriculum">
        <h3><?php _e('Kurs Müfredatı', 'woo-custom'); ?></h3>
        
        <div class="curriculum-accordion">
            <?php foreach ($course_info['curriculum'] as $index => $topic): ?>
            <div class="curriculum-topic" data-topic-id="<?php echo esc_attr($topic['id']); ?>">
                <div class="topic-header" onclick="toggleTopic(<?php echo esc_attr($topic['id']); ?>)">
                    <h4 class="topic-title">
                        <span class="topic-number"><?php echo ($index + 1); ?>.</span>
                        <?php echo esc_html($topic['title']); ?>
                        <span class="topic-count">(<?php echo count($topic['contents']); ?> <?php _e('içerik', 'woo-custom'); ?>)</span>
                    </h4>
                    <span class="topic-toggle">
                        <i class="dashicons dashicons-arrow-down-alt2"></i>
                    </span>
                </div>
                
                <div class="topic-content" id="topic-content-<?php echo esc_attr($topic['id']); ?>" style="display: none;">
                    <?php if (!empty($topic['contents'])): ?>
                    <ul class="topic-contents">
                        <?php foreach ($topic['contents'] as $content): ?>
                        <li class="content-item <?php echo $content['is_preview'] ? 'preview' : ''; ?>">
                            <div class="content-info">
                                <span class="content-type-icon">
                                    <?php
                                    switch ($content['type']) {
                                        case __('Ders', 'woo-custom'):
                                            echo '<i class="dashicons dashicons-video-alt3"></i>';
                                            break;
                                        case __('Quiz', 'woo-custom'):
                                            echo '<i class="dashicons dashicons-forms"></i>';
                                            break;
                                        case __('Ödev', 'woo-custom'):
                                            echo '<i class="dashicons dashicons-edit"></i>';
                                            break;
                                        case __('Zoom Toplantısı', 'woo-custom'):
                                            echo '<i class="dashicons dashicons-video-alt2"></i>';
                                            break;
                                        default:
                                            echo '<i class="dashicons dashicons-media-default"></i>';
                                    }
                                    ?>
                                </span>
                                <span class="content-title"><?php echo esc_html($content['title']); ?></span>
                                <span class="content-type"><?php echo esc_html($content['type']); ?></span>
                            </div>
                            <?php if ($content['is_preview']): ?>
                            <span class="preview-badge"><?php _e('Önizleme', 'woo-custom'); ?></span>
                            <?php endif; ?>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php else: ?>
                    <p class="no-content"><?php _e('Bu konuda henüz içerik bulunmuyor.', 'woo-custom'); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
    
</div>

<script>
function toggleTopic(topicId) {
    const content = document.getElementById('topic-content-' + topicId);
    const toggle = document.querySelector('[data-topic-id="' + topicId + '"] .topic-toggle i');
    
    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        toggle.classList.remove('dashicons-arrow-down-alt2');
        toggle.classList.add('dashicons-arrow-up-alt2');
    } else {
        content.style.display = 'none';
        toggle.classList.remove('dashicons-arrow-up-alt2');
        toggle.classList.add('dashicons-arrow-down-alt2');
    }
}

// Open first topic by default
document.addEventListener('DOMContentLoaded', function() {
    const firstTopic = document.querySelector('.curriculum-topic');
    if (firstTopic) {
        const topicId = firstTopic.getAttribute('data-topic-id');
        if (topicId) {
            toggleTopic(topicId);
        }
    }
});
</script>
