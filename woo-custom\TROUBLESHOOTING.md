# Kurs Bilgileri Entegrasyonu Sorun Giderme

## Sorun: Kurs Bilgileri sekmesi görünmüyor

### Adım 1: Debug Modunu Aktifleştirin

1. WordPress'in `wp-config.php` dosyasında debug modunu aktifleştirin:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', true);
```

2. Eklentiyi deaktif edip tekrar aktif edin.

3. Her<PERSON>i bir WooCommerce ürün sayfasına gidin.

### Adım 2: Test Sekmesini Kontrol Edin

Debug modu aktifken, ürün sayfasında şu sekmeleri görmelisiniz:
- **TEST TAB**: Bu sekme varsa, WooCommerce tab sistemi çalışıyor demektir
- **DEBUG**: Bu sekme debug bilgilerini gösterir
- **Kurs Bilgileri**: <PERSON> hedef sek<PERSON>z

### Adım 3: Debug Bilgilerini İnceleyin

**TEST TAB** sekmesinde şu bilgileri kontrol edin:
- ✓ WooCustom_Course_Info class exists
- ✓ WooCustom_Course_Info instance created
- Hook listesinde `WooCustom_Course_Info::add_course_info_tab` görünüyor mu?

### Adım 4: Tutor LMS Kontrolü

**Kurs Bilgileri** sekmesinde şu bilgileri kontrol edin:
- Tutor LMS aktif mi?
- Ürün ID'si doğru mu?
- Kurs bağlantısı var mı?

### Adım 5: Manuel Kurs Bağlantısı

Eğer otomatik bağlantı çalışmıyorsa, manuel olarak test edin:

1. WordPress Admin > Ürünler > İlgili ürünü düzenle
2. "Özel Alanlar" bölümünde şu meta key'leri ekleyin:
   - `_tutor_product` = `yes`
   - `_course_id` = `[KURS_ID_NUMARASI]`

### Adım 6: Kurs ID'sini Bulma

Kurs ID'sini bulmak için:
1. WordPress Admin > Kurslar
2. İlgili kursu düzenle
3. URL'deki `post=` parametresindeki sayı kurs ID'sidir

Örnek: `post.php?post=123&action=edit` → Kurs ID: 123

### Adım 7: Cache Temizleme

1. Tüm cache'leri temizleyin (WP Rocket, W3 Total Cache, vb.)
2. Tarayıcı cache'ini temizleyin (Ctrl+F5)
3. CDN cache'ini temizleyin (Cloudflare vb.)

### Adım 8: Tema Uyumluluğu

Bazı temalar WooCommerce tab sistemini override edebilir:

1. Geçici olarak varsayılan tema (Twenty Twenty-Four) aktif edin
2. Sorun devam ediyorsa tema sorunu değildir
3. Sorun çözülürse, tema geliştiricisi ile iletişime geçin

### Adım 9: Plugin Çakışması

1. Tüm eklentileri deaktif edin (WooCommerce ve Tutor LMS hariç)
2. Sadece Woo Custom eklentisini aktif edin
3. Sorun devam ediyorsa, eklenti çakışması değildir

### Adım 10: Log Kontrolü

WordPress debug loglarını kontrol edin:
- `/wp-content/debug.log` dosyasını inceleyin
- PHP hataları var mı?
- Fatal error'lar var mı?

## Yaygın Sorunlar ve Çözümleri

### 1. "Class 'TUTOR' not found" Hatası
**Çözüm**: Tutor LMS eklentisini kurun ve aktifleştirin.

### 2. Sekme görünüyor ama içerik boş
**Çözüm**: Template dosyası yolunu kontrol edin:
- `woo-custom/templates/course-info-tab.php` dosyası var mı?
- Dosya izinleri doğru mu? (644)

### 3. CSS stilleri uygulanmıyor
**Çözüm**: 
- `woo-custom/assets/css/woo-custom.css` dosyası yükleniyor mu?
- Tarayıcı geliştirici araçlarında CSS dosyası görünüyor mu?

### 4. Türkçe çeviriler görünmüyor
**Çözüm**:
- WordPress dili Türkçe olarak ayarlanmış mı?
- `woo-custom/languages/woo-custom-tr_TR.po` dosyası var mı?

## Test Komutları

Debug modunda şu URL'leri test edin:

1. **Genel Test**: `yoursite.com/product/matematik-2025-lti/?test_course_info=1`
2. **Debug Modu**: Herhangi bir ürün sayfasında TEST TAB sekmesini kontrol edin

## Destek

Sorun devam ederse:
1. Debug bilgilerini toplayın
2. WordPress ve eklenti versiyonlarını not edin
3. Hata loglarını kaydedin
4. Ekran görüntüleri alın

Bu bilgilerle teknik destek ekibiyle iletişime geçin.
