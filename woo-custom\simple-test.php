<?php
/**
 * Simple Test for Course Info
 *
 * Add this to functions.php temporarily to test
 */

// Test hook to add a simple tab
add_filter('woocommerce_product_tabs', function($tabs) {
    $tabs['test_tab'] = array(
        'title'    => 'TEST TAB',
        'priority' => 50,
        'callback' => function() {
            echo '<div style="padding: 20px;">';
            echo '<h3>Test Tab Working!</h3>';
            echo '<p>This proves that the woocommerce_product_tabs filter is working.</p>';
            echo '<p>Current time: ' . date('Y-m-d H:i:s') . '</p>';
            
            // Check if our class exists
            if (class_exists('WooCustom_Course_Info')) {
                echo '<p style="color: green;">✓ WooCustom_Course_Info class exists</p>';
                
                $instance = WooCustom_Course_Info::instance();
                if ($instance) {
                    echo '<p style="color: green;">✓ WooCustom_Course_Info instance created</p>';
                } else {
                    echo '<p style="color: red;">✗ WooCustom_Course_Info instance failed</p>';
                }
            } else {
                echo '<p style="color: red;">✗ WooCustom_Course_Info class does not exist</p>';
            }
            
            // Check hooks
            global $wp_filter;
            if (isset($wp_filter['woocommerce_product_tabs'])) {
                echo '<p>Registered hooks for woocommerce_product_tabs:</p>';
                echo '<ul>';
                foreach ($wp_filter['woocommerce_product_tabs']->callbacks as $priority => $callbacks) {
                    foreach ($callbacks as $callback) {
                        if (is_array($callback['function'])) {
                            if (is_object($callback['function'][0])) {
                                $class_name = get_class($callback['function'][0]);
                                $method_name = $callback['function'][1];
                                echo '<li>Priority ' . $priority . ': ' . $class_name . '::' . $method_name . '</li>';
                            } else {
                                echo '<li>Priority ' . $priority . ': Array callback</li>';
                            }
                        } else {
                            echo '<li>Priority ' . $priority . ': ' . $callback['function'] . '</li>';
                        }
                    }
                }
                echo '</ul>';
            }
            
            echo '</div>';
        }
    );
    
    return $tabs;
}, 99);

// Add admin notice
add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-info">';
        echo '<p><strong>Simple Test Active:</strong> Check any WooCommerce product page for "TEST TAB"</p>';
        echo '</div>';
    }
});
?>
