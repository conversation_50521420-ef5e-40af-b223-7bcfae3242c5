<?php
/**
 * Test Course Info Integration
 *
 * This file tests the Tutor LMS course info integration with WooCommerce
 *
 * @package WooCustom
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Course Info Integration
 */
class WooCustom_Course_Info_Test {
    
    public function __construct() {
        add_action('init', array($this, 'run_tests'));
    }
    
    /**
     * Run all tests
     */
    public function run_tests() {
        // Only run tests if user is admin and test parameter is set
        if (!current_user_can('manage_options') || !isset($_GET['test_course_info'])) {
            return;
        }
        
        echo '<div style="background: white; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h2>Woo Custom - Course Info Integration Test</h2>';
        
        $this->test_plugin_loaded();
        $this->test_tutor_lms_active();
        $this->test_woocommerce_active();
        $this->test_course_info_class();
        $this->test_hooks_registered();
        $this->test_sample_data();
        
        echo '</div>';
        exit;
    }
    
    /**
     * Test if plugin is loaded
     */
    private function test_plugin_loaded() {
        echo '<h3>1. Plugin Yükleme Testi</h3>';
        
        if (defined('WOO_CUSTOM_VERSION')) {
            echo '<p style="color: green;">✓ Woo Custom eklentisi yüklendi (Versiyon: ' . WOO_CUSTOM_VERSION . ')</p>';
        } else {
            echo '<p style="color: red;">✗ Woo Custom eklentisi yüklenmedi</p>';
        }
        
        if (class_exists('WooCustom_Course_Info')) {
            echo '<p style="color: green;">✓ WooCustom_Course_Info sınıfı yüklendi</p>';
        } else {
            echo '<p style="color: red;">✗ WooCustom_Course_Info sınıfı yüklenmedi</p>';
        }
    }
    
    /**
     * Test if Tutor LMS is active
     */
    private function test_tutor_lms_active() {
        echo '<h3>2. Tutor LMS Testi</h3>';
        
        if (class_exists('TUTOR')) {
            echo '<p style="color: green;">✓ Tutor LMS aktif</p>';
            
            if (function_exists('tutor_utils')) {
                echo '<p style="color: green;">✓ tutor_utils() fonksiyonu mevcut</p>';
            } else {
                echo '<p style="color: orange;">⚠ tutor_utils() fonksiyonu mevcut değil</p>';
            }
            
            if (function_exists('get_tutor_course_level')) {
                echo '<p style="color: green;">✓ get_tutor_course_level() fonksiyonu mevcut</p>';
            } else {
                echo '<p style="color: orange;">⚠ get_tutor_course_level() fonksiyonu mevcut değil</p>';
            }
        } else {
            echo '<p style="color: red;">✗ Tutor LMS aktif değil</p>';
        }
    }
    
    /**
     * Test if WooCommerce is active
     */
    private function test_woocommerce_active() {
        echo '<h3>3. WooCommerce Testi</h3>';
        
        if (class_exists('WooCommerce')) {
            echo '<p style="color: green;">✓ WooCommerce aktif</p>';
            
            if (function_exists('wc_get_product')) {
                echo '<p style="color: green;">✓ wc_get_product() fonksiyonu mevcut</p>';
            } else {
                echo '<p style="color: red;">✗ wc_get_product() fonksiyonu mevcut değil</p>';
            }
        } else {
            echo '<p style="color: red;">✗ WooCommerce aktif değil</p>';
        }
    }
    
    /**
     * Test course info class
     */
    private function test_course_info_class() {
        echo '<h3>4. Course Info Sınıf Testi</h3>';
        
        if (class_exists('WooCustom_Course_Info')) {
            $instance = WooCustom_Course_Info::instance();
            
            if ($instance) {
                echo '<p style="color: green;">✓ WooCustom_Course_Info instance oluşturuldu</p>';
            } else {
                echo '<p style="color: red;">✗ WooCustom_Course_Info instance oluşturulamadı</p>';
            }
        }
    }
    
    /**
     * Test if hooks are registered
     */
    private function test_hooks_registered() {
        echo '<h3>5. Hook Kayıt Testi</h3>';
        
        global $wp_filter;
        
        if (isset($wp_filter['woocommerce_product_tabs'])) {
            $found_hook = false;
            foreach ($wp_filter['woocommerce_product_tabs']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'WooCustom_Course_Info' &&
                        $callback['function'][1] === 'add_course_info_tab') {
                        $found_hook = true;
                        echo '<p style="color: green;">✓ woocommerce_product_tabs hook kayıtlı (Öncelik: ' . $priority . ')</p>';
                        break 2;
                    }
                }
            }
            
            if (!$found_hook) {
                echo '<p style="color: red;">✗ woocommerce_product_tabs hook kayıtlı değil</p>';
            }
        } else {
            echo '<p style="color: red;">✗ woocommerce_product_tabs hook bulunamadı</p>';
        }
    }
    
    /**
     * Test with sample data
     */
    private function test_sample_data() {
        echo '<h3>6. Örnek Veri Testi</h3>';
        
        // Check for Tutor products
        $tutor_products = get_posts(array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_tutor_product',
                    'value' => 'yes',
                    'compare' => '='
                )
            ),
            'posts_per_page' => 5
        ));
        
        if (!empty($tutor_products)) {
            echo '<p style="color: green;">✓ ' . count($tutor_products) . ' Tutor ürünü bulundu</p>';
            
            foreach ($tutor_products as $product) {
                echo '<p style="margin-left: 20px;">- ' . $product->post_title . ' (ID: ' . $product->ID . ')</p>';
                
                // Check if course exists for this product
                global $wpdb;
                $course_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT post_id FROM {$wpdb->postmeta} 
                     WHERE meta_key = '_tutor_course_product_id' 
                     AND meta_value = %d",
                    $product->ID
                ));
                
                if ($course_id) {
                    echo '<p style="margin-left: 40px; color: green;">✓ Bağlı kurs bulundu (ID: ' . $course_id . ')</p>';
                } else {
                    echo '<p style="margin-left: 40px; color: orange;">⚠ Bağlı kurs bulunamadı</p>';
                }
            }
        } else {
            echo '<p style="color: orange;">⚠ Tutor ürünü bulunamadı</p>';
        }
        
        // Check for courses
        $courses = get_posts(array(
            'post_type' => 'courses',
            'posts_per_page' => 5
        ));
        
        if (!empty($courses)) {
            echo '<p style="color: green;">✓ ' . count($courses) . ' kurs bulundu</p>';
        } else {
            echo '<p style="color: orange;">⚠ Kurs bulunamadı</p>';
        }
    }
}

// Initialize test if needed
if (isset($_GET['test_course_info'])) {
    new WooCustom_Course_Info_Test();
}

// Add admin notice for easy testing
add_action('admin_notices', function() {
    if (current_user_can('manage_options')) {
        $test_url = add_query_arg('test_course_info', '1', home_url());
        echo '<div class="notice notice-info">';
        echo '<p><strong>Woo Custom Course Info Test:</strong> ';
        echo '<a href="' . esc_url($test_url) . '" target="_blank">Kurs bilgileri entegrasyonunu test et</a>';
        echo '</p>';
        echo '</div>';
    }
});
?>
